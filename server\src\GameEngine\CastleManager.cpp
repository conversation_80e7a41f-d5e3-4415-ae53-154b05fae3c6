// CastleManager.cpp - 城堡管理器实现
#include "CastleManager.h"
#include "GuildManager.h"
#include "Environment.h"
#include "MapManager.h"
#include "../BaseObject/BaseObject.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Utils.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <algorithm>

namespace MirServer {

// 全局城堡管理器实例
std::unique_ptr<CastleManager> g_CastleManager;

// ============================================================================
// Castle 类实现
// ============================================================================

Castle::Castle(const std::string& configDir)
    : m_configDir(configDir)
    , m_masterGuild(nullptr)
    , m_homeX(644)
    , m_homeY(290)
    , m_palaceDoorX(631)
    , m_palaceDoorY(274)
    , m_warStatus(CastleWarStatus::PEACE)
    , m_isUnderAttack(false)
    , m_showWarEndMsg(false)
    , m_warStartTime(0)
    , m_warEndTime(0)
    , m_warRangeX(50)
    , m_warRangeY(50)
    , m_totalGold(0)
    , m_todayIncome(0)
    , m_incomeDate(0)
    , m_taxRate(0)
    , m_techLevel(0)
    , m_power(0)
    , m_lastSaveTime(0)
    , m_needSave(false) {

    // 初始化防御单元
    m_archers.resize(12);  // 最大12个弓箭手
    m_guards.resize(4);    // 最大4个守卫

    // 设置默认值
    m_castleName = "沙巴克";
    m_mapName = "3";
    m_homeMap = "3";
    m_palaceMap = "0150";
    m_secretMap = "D701";
}

Castle::~Castle() {
    Finalize();
}

bool Castle::Initialize() {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    // 加载配置
    if (!LoadConfig()) {
        Logger::Error("Failed to load castle config: " + m_configDir);
        return false;
    }

    // 加载攻击者列表
    LoadAttackerList();

    // 初始化防御单元
    InitializeDefenseUnits();

    // 查找拥有者行会
    if (!m_ownerGuild.empty()) {
        m_masterGuild = GuildManager::GetInstance().FindGuild(m_ownerGuild);
    }

    Logger::Info("Castle initialized: " + m_castleName);
    return true;
}

void Castle::Finalize() {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    // 保存配置
    if (m_needSave) {
        SaveConfig();
        SaveAttackerList();
    }

    // 清理防御单元
    m_mainDoor.object.reset();
    m_leftWall.object.reset();
    m_centerWall.object.reset();
    m_rightWall.object.reset();

    for (auto& archer : m_archers) {
        archer.object.reset();
    }

    for (auto& guard : m_guards) {
        guard.object.reset();
    }

    Logger::Info("Castle finalized: " + m_castleName);
}

bool Castle::LoadConfig() {
    std::string configFile = "Castle/" + m_configDir + "/SabukW.txt";

    if (!std::filesystem::exists(configFile)) {
        Logger::Warning("Castle config file not found: " + configFile);
        return CreateDefaultConfig();
    }

    std::ifstream file(configFile);
    if (!file.is_open()) {
        Logger::Error("Failed to open castle config file: " + configFile);
        return false;
    }

    std::string line, section;
    while (std::getline(file, line)) {
        line = MirServer::Trim(line);
        if (line.empty() || line[0] == ';') continue;

        if (line[0] == '[' && line.back() == ']') {
            section = line.substr(1, line.length() - 2);
            continue;
        }

        size_t pos = line.find('=');
        if (pos == std::string::npos) continue;

        std::string key = MirServer::Trim(line.substr(0, pos));
        std::string value = MirServer::Trim(line.substr(pos + 1));

        if (section == "Setup") {
            if (key == "CastleName") m_castleName = value;
            else if (key == "OwnGuild") m_ownerGuild = value;
            else if (key == "TotalGold") m_totalGold = std::stoi(value);
            else if (key == "TodayIncome") m_todayIncome = std::stoi(value);
        }
        else if (section == "Defense") {
            if (key == "CastleMap") m_mapName = value;
            else if (key == "CastleHomeMap") m_homeMap = value;
            else if (key == "CastleHomeX") m_homeX = std::stoi(value);
            else if (key == "CastleHomeY") m_homeY = std::stoi(value);
            else if (key == "CastleWarRangeX") m_warRangeX = std::stoi(value);
            else if (key == "CastleWarRangeY") m_warRangeY = std::stoi(value);
            else if (key == "CastlePlaceMap") m_palaceMap = value;
            else if (key == "CastleSecretMap") m_secretMap = value;
            else if (key == "CastlePalaceDoorX") m_palaceDoorX = std::stoi(value);
            else if (key == "CastlePalaceDoorY") m_palaceDoorY = std::stoi(value);
            // 加载防御单元配置
            else if (key.find("MainDoor") == 0) {
                LoadDefenseUnitConfig("MainDoor", m_mainDoor);
            }
            else if (key.find("LeftWall") == 0) {
                LoadDefenseUnitConfig("LeftWall", m_leftWall);
            }
            else if (key.find("CenterWall") == 0) {
                LoadDefenseUnitConfig("CenterWall", m_centerWall);
            }
            else if (key.find("RightWall") == 0) {
                LoadDefenseUnitConfig("RightWall", m_rightWall);
            }
        }
    }

    file.close();
    return true;
}

bool Castle::CreateDefaultConfig() {
    // 创建默认配置
    m_castleName = "Sabuk";  // 使用英文名避免编码问题
    m_ownerGuild = "";
    m_mapName = "3";
    m_homeMap = "3";
    m_homeX = 644;
    m_homeY = 290;
    m_warRangeX = 50;
    m_warRangeY = 50;
    m_palaceMap = "0150";
    m_secretMap = "D701";
    m_palaceDoorX = 631;
    m_palaceDoorY = 274;

    // 设置默认防御单元
    m_mainDoor.x = 672;
    m_mainDoor.y = 330;
    m_mainDoor.name = "MainDoor";
    m_mainDoor.type = DefenseUnitType::MAIN_DOOR;
    m_mainDoor.hp = m_mainDoor.maxHp = 2000;
    m_mainDoor.status = DefenseUnitStatus::NORMAL;

    m_leftWall.x = 624;
    m_leftWall.y = 278;
    m_leftWall.name = "LeftWall";
    m_leftWall.type = DefenseUnitType::LEFT_WALL;
    m_leftWall.hp = m_leftWall.maxHp = 2000;
    m_leftWall.status = DefenseUnitStatus::NORMAL;

    m_centerWall.x = 627;
    m_centerWall.y = 278;
    m_centerWall.name = "CenterWall";
    m_centerWall.type = DefenseUnitType::CENTER_WALL;
    m_centerWall.hp = m_centerWall.maxHp = 2000;
    m_centerWall.status = DefenseUnitStatus::NORMAL;

    m_rightWall.x = 634;
    m_rightWall.y = 271;
    m_rightWall.name = "RightWall";
    m_rightWall.type = DefenseUnitType::RIGHT_WALL;
    m_rightWall.hp = m_rightWall.maxHp = 2000;
    m_rightWall.status = DefenseUnitStatus::NORMAL;

    m_needSave = true;
    return true;
}

bool Castle::SaveConfig() {
    std::string configDir = "Castle/" + m_configDir;
    if (!std::filesystem::exists(configDir)) {
        std::filesystem::create_directories(configDir);
    }

    std::string configFile = configDir + "/SabukW.txt";
    std::ofstream file(configFile);
    if (!file.is_open()) {
        Logger::Error("Failed to create castle config file: " + configFile);
        return false;
    }

    // 写入配置
    file << "[Setup]\n";
    file << "CastleName=" << m_castleName << "\n";
    file << "OwnGuild=" << m_ownerGuild << "\n";
    file << "TotalGold=" << m_totalGold << "\n";
    file << "TodayIncome=" << m_todayIncome << "\n";
    file << "\n";

    file << "[Defense]\n";
    file << "CastleMap=" << m_mapName << "\n";
    file << "CastleHomeMap=" << m_homeMap << "\n";
    file << "CastleHomeX=" << m_homeX << "\n";
    file << "CastleHomeY=" << m_homeY << "\n";
    file << "CastleWarRangeX=" << m_warRangeX << "\n";
    file << "CastleWarRangeY=" << m_warRangeY << "\n";
    file << "CastlePlaceMap=" << m_palaceMap << "\n";
    file << "CastleSecretMap=" << m_secretMap << "\n";
    file << "CastlePalaceDoorX=" << m_palaceDoorX << "\n";
    file << "CastlePalaceDoorY=" << m_palaceDoorY << "\n";
    file << "\n";

    // 保存防御单元配置
    SaveDefenseUnitConfig("MainDoor", m_mainDoor);
    SaveDefenseUnitConfig("LeftWall", m_leftWall);
    SaveDefenseUnitConfig("CenterWall", m_centerWall);
    SaveDefenseUnitConfig("RightWall", m_rightWall);

    file.close();
    m_needSave = false;
    return true;
}

void Castle::LoadAttackerList() {
    std::string attackerFile = "Castle/" + m_configDir + "/AttackSabukWall.txt";

    if (!std::filesystem::exists(attackerFile)) {
        return; // 文件不存在，跳过
    }

    std::ifstream file(attackerFile);
    if (!file.is_open()) {
        Logger::Warning("Failed to open attacker list file: " + attackerFile);
        return;
    }

    std::string line;
    while (std::getline(file, line)) {
        line = MirServer::Trim(line);
        if (line.empty()) continue;

        // 格式: GuildName|AttackDate
        size_t pos = line.find('|');
        if (pos != std::string::npos) {
            std::string guildName = line.substr(0, pos);
            DWORD attackDate = std::stoul(line.substr(pos + 1));

            AttackerInfo info(guildName, attackDate, nullptr);
            info.guild = GuildManager::GetInstance().FindGuild(guildName);
            m_attackerList.push_back(info);
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(m_attackerList.size()) + " attackers for castle: " + m_castleName);
}

void Castle::SaveAttackerList() {
    std::string configDir = "Castle/" + m_configDir;
    if (!std::filesystem::exists(configDir)) {
        std::filesystem::create_directories(configDir);
    }

    std::string attackerFile = configDir + "/AttackSabukWall.txt";
    std::ofstream file(attackerFile);
    if (!file.is_open()) {
        Logger::Error("Failed to create attacker list file: " + attackerFile);
        return;
    }

    for (const auto& attacker : m_attackerList) {
        file << attacker.guildName << "|" << attacker.attackDate << "\n";
    }

    file.close();
}

bool Castle::LoadDefenseUnitConfig(const std::string& section, DefenseUnit& unit) {
    // 这个方法在LoadConfig中被调用，用于加载特定防御单元的配置
    // 实际的解析逻辑已经在LoadConfig中处理
    return true;
}

void Castle::SaveDefenseUnitConfig(const std::string& section, const DefenseUnit& unit) {
    // 这个方法在SaveConfig中被调用，用于保存特定防御单元的配置
    // 实际的保存逻辑已经在SaveConfig中处理
}

void Castle::InitializeDefenseUnits() {
    // 创建防御单元对象
    CreateDefenseUnit(m_mainDoor, m_mapName);
    CreateDefenseUnit(m_leftWall, m_mapName);
    CreateDefenseUnit(m_centerWall, m_mapName);
    CreateDefenseUnit(m_rightWall, m_mapName);

    // 创建弓箭手和守卫
    for (auto& archer : m_archers) {
        if (archer.hp > 0) {
            CreateDefenseUnit(archer, m_mapName);
        }
    }

    for (auto& guard : m_guards) {
        if (guard.hp > 0) {
            CreateDefenseUnit(guard, m_mapName);
        }
    }
}

void Castle::CreateDefenseUnit(DefenseUnit& unit, const std::string& mapName) {
    if (unit.name.empty() || unit.hp <= 0) {
        return;
    }

    // TODO: 这里需要与UserEngine集成来创建实际的游戏对象
    // unit.object = UserEngine::RegenMonsterByName(mapName, unit.x, unit.y, unit.name);
    // if (unit.object) {
    //     unit.object->SetHP(unit.hp);
    //     unit.object->SetCastle(this);
    // }

    Logger::Info("Created defense unit: " + unit.name + " at (" +
                std::to_string(unit.x) + "," + std::to_string(unit.y) + ")");
}

bool Castle::InCastleWarArea(Environment* env, int x, int y) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    if (!env || env->GetMapName() != m_mapName) {
        return false;
    }

    // 检查是否在战争范围内
    int centerX = m_homeX;
    int centerY = m_homeY;

    return (abs(x - centerX) <= m_warRangeX && abs(y - centerY) <= m_warRangeY);
}

bool Castle::IsMember(BaseObject* obj) const {
    if (!obj) return false;

    std::shared_lock<std::shared_mutex> lock(m_mutex);

    // TODO: 检查对象是否是城堡成员
    // 需要与PlayObject和Guild系统集成
    return false;
}

bool Castle::IsMasterGuild(Guild* guild) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return guild && m_masterGuild && guild == m_masterGuild;
}

bool Castle::IsAttackGuild(Guild* guild) const {
    if (!guild) return false;

    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return InAttackerList(guild);
}

bool Castle::IsDefenseGuild(Guild* guild) const {
    return IsMasterGuild(guild);
}

bool Castle::CanGetCastle(Guild* guild) const {
    if (!guild) return false;

    std::shared_lock<std::shared_mutex> lock(m_mutex);

    // 检查是否在战争状态
    if (m_warStatus != CastleWarStatus::ACTIVE) {
        return false;
    }

    // 检查是否是攻击行会（内部调用，不再获取锁）
    if (!InAttackerList(guild)) {
        return false;
    }

    // TODO: 检查其他条件，如是否在皇宫内等
    return true;
}

void Castle::GetCastle(Guild* guild) {
    if (!guild || !CanGetCastle(guild)) {
        return;
    }

    std::unique_lock<std::shared_mutex> lock(m_mutex);

    // 更换城堡拥有者
    m_ownerGuild = guild->GetGuildName();
    m_masterGuild = guild;

    // 停止战争（内部实现，不再获取锁）
    m_warStatus = CastleWarStatus::PEACE;
    m_isUnderAttack = false;
    m_warStartTime = 0;
    m_warEndTime = 0;
    m_showWarEndMsg = false;

    // 清空攻击者列表
    m_attackerList.clear();
    m_attackGuildList.clear();

    m_needSave = true;

    Logger::Info("Castle " + m_castleName + " captured by guild: " + guild->GetGuildName());
}

bool Castle::AddAttacker(const std::string& guildName, DWORD attackDate) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    // 检查是否已经存在
    for (const auto& attacker : m_attackerList) {
        if (attacker.guildName == guildName) {
            return false; // 已经存在
        }
    }

    // 查找行会
    Guild* guild = GuildManager::GetInstance().FindGuild(guildName);
    if (!guild) {
        Logger::Warning("Guild not found when adding attacker: " + guildName);
        return false;
    }

    // 添加攻击者
    AttackerInfo info(guildName, attackDate, guild);
    m_attackerList.push_back(info);

    m_needSave = true;

    Logger::Info("Added attacker to castle " + m_castleName + ": " + guildName);
    return true;
}

bool Castle::RemoveAttacker(const std::string& guildName) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    auto it = std::remove_if(m_attackerList.begin(), m_attackerList.end(),
        [&guildName](const AttackerInfo& info) {
            return info.guildName == guildName;
        });

    if (it != m_attackerList.end()) {
        m_attackerList.erase(it, m_attackerList.end());
        m_needSave = true;
        Logger::Info("Removed attacker from castle " + m_castleName + ": " + guildName);
        return true;
    }

    return false;
}

bool Castle::IsAttacker(const std::string& guildName) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    for (const auto& attacker : m_attackerList) {
        if (attacker.guildName == guildName) {
            return true;
        }
    }

    return false;
}

std::string Castle::GetAttackerList() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    std::string result;
    for (const auto& attacker : m_attackerList) {
        if (!result.empty()) {
            result += ",";
        }
        result += attacker.guildName;
    }

    return result;
}

bool Castle::InAttackerList(Guild* guild) const {
    if (!guild) return false;

    for (const auto& attacker : m_attackerList) {
        if (attacker.guild == guild) {
            return true;
        }
    }

    return false;
}

// 实现需要锁保护的getter方法
CastleWarStatus Castle::GetWarStatus() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_warStatus;
}

bool Castle::IsUnderAttack() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_isUnderAttack;
}

int Castle::GetTotalGold() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_totalGold;
}

int Castle::GetTodayIncome() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_todayIncome;
}

int Castle::GetTechLevel() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_techLevel;
}

int Castle::GetPower() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_power;
}

// 战争相关方法
bool Castle::CanStartWar() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return m_warStatus == CastleWarStatus::PEACE && !m_attackerList.empty();
}

void Castle::StartWar() {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    if (m_warStatus != CastleWarStatus::PEACE) {
        return;
    }

    m_warStatus = CastleWarStatus::ACTIVE;
    m_isUnderAttack = true;
    m_warStartTime = MirServer::GetCurrentTime();
    m_warEndTime = m_warStartTime + (3 * 60 * 60 * 1000); // 3小时
    m_showWarEndMsg = false;

    m_needSave = true;

    Logger::Info("Castle war started for: " + m_castleName);
}

void Castle::StopWar() {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    m_warStatus = CastleWarStatus::PEACE;
    m_isUnderAttack = false;
    m_warStartTime = 0;
    m_warEndTime = 0;
    m_showWarEndMsg = false;

    m_needSave = true;

    Logger::Info("Castle war stopped for: " + m_castleName);
}

// 防御单元管理方法
bool Castle::RepairDoor() {
    return RepairUnit(DefenseUnitType::MAIN_DOOR);
}

bool Castle::RepairWall(int wallIndex) {
    DefenseUnitType wallType;
    switch (wallIndex) {
        case 0: wallType = DefenseUnitType::LEFT_WALL; break;
        case 1: wallType = DefenseUnitType::CENTER_WALL; break;
        case 2: wallType = DefenseUnitType::RIGHT_WALL; break;
        default: return false;
    }

    return RepairUnit(wallType);
}

bool Castle::RepairUnit(DefenseUnitType type, int index) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    DefenseUnit* unit = nullptr;

    switch (type) {
        case DefenseUnitType::MAIN_DOOR:
            unit = &m_mainDoor;
            break;
        case DefenseUnitType::LEFT_WALL:
            unit = &m_leftWall;
            break;
        case DefenseUnitType::CENTER_WALL:
            unit = &m_centerWall;
            break;
        case DefenseUnitType::RIGHT_WALL:
            unit = &m_rightWall;
            break;
        case DefenseUnitType::ARCHER:
            if (index >= 0 && index < static_cast<int>(m_archers.size())) {
                unit = &m_archers[index];
            }
            break;
        case DefenseUnitType::GUARD:
            if (index >= 0 && index < static_cast<int>(m_guards.size())) {
                unit = &m_guards[index];
            }
            break;
    }

    if (!unit) {
        return false;
    }

    // 修复单元
    unit->hp = unit->maxHp;
    unit->status = DefenseUnitStatus::NORMAL;

    // 如果有对应的游戏对象，也修复它
    if (unit->object) {
        // TODO: 设置游戏对象的HP
        // unit->object->SetHP(unit->hp);
    }

    m_needSave = true;

    Logger::Info("Repaired defense unit: " + unit->name + " in castle: " + m_castleName);
    return true;
}

void Castle::MainDoorControl(bool open) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    if (m_mainDoor.object) {
        // TODO: 控制门的开关
        // if (open) {
        //     static_cast<CastleDoor*>(m_mainDoor.object.get())->Open();
        // } else {
        //     static_cast<CastleDoor*>(m_mainDoor.object.get())->Close();
        // }
    }

    Logger::Info(std::string("Castle door ") + (open ? "opened" : "closed") + " for: " + m_castleName);
}

// 经济系统方法
void Castle::IncomeGold(int gold) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    m_totalGold += gold;
    m_todayIncome += gold;
    m_needSave = true;

    Logger::Info("Castle " + m_castleName + " received gold: " + std::to_string(gold));
}

int Castle::WithdrawGold(PlayObject* player, int gold) {
    if (!player || gold <= 0) {
        return 0;
    }

    std::unique_lock<std::shared_mutex> lock(m_mutex);

    // TODO: 检查玩家权限
    // if (!player->IsCastleMaster()) {
    //     return 0;
    // }

    int actualGold = std::min(gold, m_totalGold);
    if (actualGold > 0) {
        m_totalGold -= actualGold;
        m_needSave = true;

        // TODO: 给玩家添加金币
        // player->AddGold(actualGold);

        Logger::Info("Withdrew " + std::to_string(actualGold) + " gold from castle: " + m_castleName);
    }

    return actualGold;
}

int Castle::DepositGold(PlayObject* player, int gold) {
    if (!player || gold <= 0) {
        return 0;
    }

    std::unique_lock<std::shared_mutex> lock(m_mutex);

    // TODO: 检查玩家权限和金币
    // if (!player->IsCastleMember() || !player->DecGold(gold)) {
    //     return 0;
    // }

    m_totalGold += gold;
    m_needSave = true;

    Logger::Info("Deposited " + std::to_string(gold) + " gold to castle: " + m_castleName);
    return gold;
}

// 技术和力量方法
void Castle::SetTechLevel(int level) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    if (level >= 0 && level <= 10) { // 假设最大技术等级为10
        m_techLevel = level;
        m_needSave = true;

        Logger::Info("Castle " + m_castleName + " tech level set to: " + std::to_string(level));
    }
}

void Castle::SetPower(int power) {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    if (power >= 0) {
        m_power = power;
        m_needSave = true;

        Logger::Info("Castle " + m_castleName + " power set to: " + std::to_string(power));
    }
}

// 运行时处理方法
void Castle::Run() {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    DWORD currentTime = MirServer::GetCurrentTime();

    // 处理战争逻辑
    ProcessWarLogic();

    // 处理收入逻辑
    ProcessIncomeLogic();

    // 更新防御单元状态
    UpdateDefenseUnitStatus();

    // 定期保存
    if (m_needSave && (currentTime - m_lastSaveTime) > 60000) { // 每分钟保存一次
        Save();
        m_lastSaveTime = currentTime;
    }
}

void Castle::Save() {
    if (m_needSave) {
        SaveConfig();
        SaveAttackerList();
        m_needSave = false;
    }
}

void Castle::ProcessWarLogic() {
    if (m_warStatus != CastleWarStatus::ACTIVE) {
        return;
    }

    DWORD currentTime = MirServer::GetCurrentTime();

    // 检查战争是否结束
    if (currentTime >= m_warEndTime) {
        // 停止战争（内部实现，不再获取锁）
        m_warStatus = CastleWarStatus::PEACE;
        m_isUnderAttack = false;
        m_warStartTime = 0;
        m_warEndTime = 0;
        m_showWarEndMsg = false;
        m_needSave = true;
        Logger::Info("Castle war ended for: " + m_castleName);
    }
    // 检查是否需要显示结束提示
    else if (!m_showWarEndMsg && (m_warEndTime - currentTime) <= 600000) { // 10分钟前提示
        m_showWarEndMsg = true;
        // TODO: 发送战争即将结束的消息
        Logger::Info("Castle war ending soon for: " + m_castleName);
    }
}

void Castle::ProcessIncomeLogic() {
    DWORD currentTime = MirServer::GetCurrentTime();
    DWORD currentDate = currentTime / (24 * 60 * 60 * 1000); // 简化的日期计算

    // 检查是否是新的一天
    if (m_incomeDate != currentDate) {
        m_incomeDate = currentDate;
        m_todayIncome = 0; // 重置今日收入
        m_needSave = true;
    }
}

void Castle::UpdateDefenseUnitStatus() {
    // 更新防御单元状态
    auto updateUnit = [](DefenseUnit& unit) {
        if (unit.object) {
            // TODO: 从游戏对象获取当前HP
            // unit.hp = unit.object->GetHP();

            if (unit.hp <= 0) {
                unit.status = DefenseUnitStatus::DESTROYED;
            } else if (unit.hp < unit.maxHp / 2) {
                unit.status = DefenseUnitStatus::DAMAGED;
            } else {
                unit.status = DefenseUnitStatus::NORMAL;
            }
        }
    };

    updateUnit(m_mainDoor);
    updateUnit(m_leftWall);
    updateUnit(m_centerWall);
    updateUnit(m_rightWall);

    for (auto& archer : m_archers) {
        updateUnit(archer);
    }

    for (auto& guard : m_guards) {
        updateUnit(guard);
    }
}

bool Castle::CheckInPalace(int x, int y, BaseObject* obj) const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    // TODO: 检查是否在皇宫范围内
    // 这需要与地图系统集成
    return false;
}

int Castle::GetPalaceGuildCount() const {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    // TODO: 统计皇宫内的行会成员数量
    // 这需要与玩家管理系统集成
    return 0;
}

// ============================================================================
// CastleManager 类实现
// ============================================================================

CastleManager::CastleManager()
    : m_initialized(false) {
}

CastleManager::~CastleManager() {
    Finalize();
}

CastleManager& CastleManager::GetInstance() {
    static CastleManager instance;
    return instance;
}

bool CastleManager::Initialize(const std::string& configPath) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized) {
        return true;
    }

    m_configPath = configPath;

    // 创建配置目录
    if (!std::filesystem::exists(m_configPath)) {
        std::filesystem::create_directories(m_configPath);
    }

    // 加载城堡列表
    if (!LoadCastleList()) {
        Logger::Warning("Failed to load castle list, creating default castle");
        if (!CreateDefaultCastle()) {
            Logger::Error("Failed to create default castle");
            return false;
        }
    }

    // 初始化所有城堡
    for (auto& castle : m_castles) {
        if (!castle->Initialize()) {
            Logger::Error("Failed to initialize castle: " + castle->GetCastleName());
        }
    }

    m_initialized = true;
    Logger::Info("CastleManager initialized with " + std::to_string(m_castles.size()) + " castles");
    return true;
}

void CastleManager::Finalize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return;
    }

    // 保存所有城堡数据
    Save();

    // 清理城堡
    ClearCastles();

    m_initialized = false;
    Logger::Info("CastleManager finalized");
}

bool CastleManager::LoadCastleList() {
    std::string listFile = m_configPath + "/CastleList.txt";

    if (!std::filesystem::exists(listFile)) {
        Logger::Info("Castle list file not found: " + listFile);
        return false;
    }

    std::ifstream file(listFile);
    if (!file.is_open()) {
        Logger::Error("Failed to open castle list file: " + listFile);
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        line = MirServer::Trim(line);
        if (line.empty() || line[0] == ';') continue;

        // 创建城堡
        auto castle = std::make_unique<Castle>(line);
        m_castleMap[castle->GetCastleName()] = castle.get();
        m_castles.push_back(std::move(castle));

        Logger::Info("Loaded castle config: " + line);
    }

    file.close();
    return !m_castles.empty();
}

bool CastleManager::SaveCastleList() {
    std::string listFile = m_configPath + "/CastleList.txt";

    std::ofstream file(listFile);
    if (!file.is_open()) {
        Logger::Error("Failed to create castle list file: " + listFile);
        return false;
    }

    file << "; Castle Configuration List\n";
    file << "; Format: ConfigDirectory\n";
    file << "\n";

    for (const auto& castle : m_castles) {
        file << castle->GetCastleName() << "\n";
    }

    file.close();
    return true;
}

bool CastleManager::CreateDefaultCastle() {
    // 创建默认的沙巴克城堡
    auto castle = std::make_unique<Castle>("Sabuk");
    std::string castleName = castle->GetCastleName();

    m_castleMap[castleName] = castle.get();
    m_castles.push_back(std::move(castle));

    // 保存城堡列表
    SaveCastleList();

    Logger::Info("Created default castle: " + castleName);
    return true;
}

void CastleManager::ClearCastles() {
    m_castleMap.clear();
    m_castles.clear();
}

Castle* CastleManager::FindCastle(const std::string& castleName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_castleMap.find(castleName);
    return (it != m_castleMap.end()) ? it->second : nullptr;
}

Castle* CastleManager::GetCastle(int index) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (index >= 0 && index < static_cast<int>(m_castles.size())) {
        return m_castles[index].get();
    }

    return nullptr;
}

int CastleManager::GetCastleCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return static_cast<int>(m_castles.size());
}

Castle* CastleManager::InCastleWarArea(BaseObject* obj) {
    if (!obj) return nullptr;

    // TODO: 获取对象的环境和位置
    // Environment* env = obj->GetEnvironment();
    // Point pos = obj->GetCurrentPos();
    // return InCastleWarArea(env, pos.x, pos.y);

    return nullptr;
}

Castle* CastleManager::InCastleWarArea(Environment* env, int x, int y) {
    std::lock_guard<std::mutex> lock(m_mutex);

    for (auto& castle : m_castles) {
        if (castle->InCastleWarArea(env, x, y)) {
            return castle.get();
        }
    }

    return nullptr;
}

Castle* CastleManager::IsCastleMember(BaseObject* obj) {
    std::lock_guard<std::mutex> lock(m_mutex);

    for (auto& castle : m_castles) {
        if (castle->IsMember(obj)) {
            return castle.get();
        }
    }

    return nullptr;
}

Castle* CastleManager::IsCastlePalaceEnvironment(Environment* env) {
    if (!env) return nullptr;

    std::lock_guard<std::mutex> lock(m_mutex);

    // TODO: 检查环境是否是城堡皇宫
    // 需要与地图系统集成
    return nullptr;
}

Castle* CastleManager::IsCastleEnvironment(Environment* env) {
    if (!env) return nullptr;

    std::lock_guard<std::mutex> lock(m_mutex);

    // TODO: 检查环境是否是城堡地图
    // 需要与地图系统集成
    return nullptr;
}

void CastleManager::GetCastleGoldInfo(std::vector<std::string>& info) {
    std::lock_guard<std::mutex> lock(m_mutex);

    info.clear();
    for (const auto& castle : m_castles) {
        std::string goldInfo = castle->GetCastleName() + ": " +
                              std::to_string(castle->GetTotalGold()) + " (今日: " +
                              std::to_string(castle->GetTodayIncome()) + ")";
        info.push_back(goldInfo);
    }
}

void CastleManager::GetCastleNameList(std::vector<std::string>& names) {
    std::lock_guard<std::mutex> lock(m_mutex);

    names.clear();
    for (const auto& castle : m_castles) {
        names.push_back(castle->GetCastleName());
    }
}

void CastleManager::IncomeGold(int gold) {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 将金币分配给所有城堡
    int goldPerCastle = gold / static_cast<int>(m_castles.size());
    if (goldPerCastle > 0) {
        for (auto& castle : m_castles) {
            castle->IncomeGold(goldPerCastle);
        }
    }
}

void CastleManager::Run() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return;
    }

    // 运行所有城堡的逻辑
    for (auto& castle : m_castles) {
        castle->Run();
    }
}

void CastleManager::Save() {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 保存城堡列表
    SaveCastleList();

    // 保存所有城堡数据
    for (auto& castle : m_castles) {
        castle->Save();
    }
}

} // namespace MirServer
