#pragma once

#include "../Common/Types.h"
#include "../Common/Logger.h"
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <mutex>
#include <shared_mutex>

namespace MirServer {

// 前向声明
class PlayObject;
class BaseObject;
class Environment;
class Guild;

// 城堡防御单元类型
enum class DefenseUnitType {
    MAIN_DOOR = 0,      // 主门
    LEFT_WALL = 1,      // 左墙
    CENTER_WALL = 2,    // 中墙
    RIGHT_WALL = 3,     // 右墙
    ARCHER = 4,         // 弓箭手
    GUARD = 5           // 守卫
};

// 城堡防御单元状态
enum class DefenseUnitStatus {
    NORMAL = 0,         // 正常
    DAMAGED = 1,        // 受损
    DESTROYED = 2,      // 被摧毁
    REPAIRING = 3       // 修复中
};

// 城堡战争状态
enum class CastleWarStatus {
    PEACE = 0,          // 和平时期
    PREPARING = 1,      // 准备阶段
    ACTIVE = 2,         // 战争进行中
    ENDING = 3          // 战争结束阶段
};

// 攻击者信息
struct AttackerInfo {
    std::string guildName;      // 行会名称
    DWORD attackDate;           // 攻击日期
    Guild* guild;               // 行会指针

    AttackerInfo() : attackDate(0), guild(nullptr) {}
    AttackerInfo(const std::string& name, DWORD date, Guild* g)
        : guildName(name), attackDate(date), guild(g) {}
};

// 防御单元信息
struct DefenseUnit {
    int x, y;                           // 位置坐标
    std::string name;                   // 单元名称
    DefenseUnitType type;               // 单元类型
    DefenseUnitStatus status;           // 单元状态
    int hp;                             // 当前生命值
    int maxHp;                          // 最大生命值
    std::shared_ptr<BaseObject> object; // 对应的游戏对象

    DefenseUnit() : x(0), y(0), type(DefenseUnitType::MAIN_DOOR),
                   status(DefenseUnitStatus::NORMAL), hp(0), maxHp(0) {}
};

// 城堡类（对应原项目的TUserCastle）
class Castle {
private:
    std::string m_castleName;           // 城堡名称
    std::string m_configDir;            // 配置目录
    std::string m_ownerGuild;           // 拥有者行会名称
    Guild* m_masterGuild;               // 拥有者行会指针

    // 地图信息
    std::string m_mapName;              // 城堡地图名称
    std::string m_homeMap;              // 回城地图名称
    std::string m_palaceMap;            // 皇宫地图名称
    std::string m_secretMap;            // 密道地图名称
    int m_homeX, m_homeY;               // 回城坐标
    int m_palaceDoorX, m_palaceDoorY;   // 皇宫门坐标

    // 战争相关
    CastleWarStatus m_warStatus;        // 战争状态
    bool m_isUnderAttack;               // 是否正在被攻击
    bool m_showWarEndMsg;               // 是否显示战争结束消息
    DWORD m_warStartTime;               // 战争开始时间
    DWORD m_warEndTime;                 // 战争结束时间
    int m_warRangeX, m_warRangeY;       // 战争范围

    // 防御单元
    DefenseUnit m_mainDoor;             // 主门
    DefenseUnit m_leftWall;             // 左墙
    DefenseUnit m_centerWall;           // 中墙
    DefenseUnit m_rightWall;            // 右墙
    std::vector<DefenseUnit> m_archers; // 弓箭手列表
    std::vector<DefenseUnit> m_guards;  // 守卫列表

    // 攻击者列表
    std::vector<AttackerInfo> m_attackerList;
    std::vector<std::string> m_attackGuildList;

    // 经济系统
    int m_totalGold;                    // 总金币
    int m_todayIncome;                  // 今日收入
    DWORD m_incomeDate;                 // 收入日期
    int m_taxRate;                      // 税率

    // 技术等级和力量
    int m_techLevel;                    // 技术等级
    int m_power;                        // 力量值

    // 环境列表
    std::vector<std::string> m_environmentList;

    // 线程安全
    mutable std::shared_mutex m_mutex;

    // 保存相关
    DWORD m_lastSaveTime;               // 上次保存时间
    bool m_needSave;                    // 是否需要保存

public:
    Castle(const std::string& configDir);
    ~Castle();

    // 初始化和清理
    bool Initialize();
    void Finalize();

    // 配置文件操作
    bool LoadConfig();
    bool SaveConfig();
    void LoadAttackerList();
    void SaveAttackerList();

    // 基本信息
    const std::string& GetCastleName() const { return m_castleName; }
    const std::string& GetOwnerGuild() const { return m_ownerGuild; }
    const std::string& GetMapName() const { return m_mapName; }
    const std::string& GetHomeMap() const { return m_homeMap; }
    int GetHomeX() const { return m_homeX; }
    int GetHomeY() const { return m_homeY; }

    // 战争相关
    CastleWarStatus GetWarStatus() const;
    bool IsUnderAttack() const;
    bool InCastleWarArea(Environment* env, int x, int y) const;
    bool CanStartWar() const;
    void StartWar();
    void StopWar();

    // 行会相关
    bool IsMember(BaseObject* obj) const;
    bool IsMasterGuild(Guild* guild) const;
    bool IsAttackGuild(Guild* guild) const;
    bool IsDefenseGuild(Guild* guild) const;
    bool CanGetCastle(Guild* guild) const;
    void GetCastle(Guild* guild);

    // 攻击者管理
    bool AddAttacker(const std::string& guildName, DWORD attackDate);
    bool RemoveAttacker(const std::string& guildName);
    bool IsAttacker(const std::string& guildName) const;
    std::string GetAttackerList() const;

    // 防御单元管理
    bool RepairDoor();
    bool RepairWall(int wallIndex);
    bool RepairUnit(DefenseUnitType type, int index = 0);
    void MainDoorControl(bool open);

    // 经济系统
    void IncomeGold(int gold);
    int WithdrawGold(PlayObject* player, int gold);
    int DepositGold(PlayObject* player, int gold);
    int GetTotalGold() const;
    int GetTodayIncome() const;

    // 技术和力量
    int GetTechLevel() const;
    void SetTechLevel(int level);
    int GetPower() const;
    void SetPower(int power);

    // 运行时处理
    void Run();
    void Save();

    // 检查函数
    bool CheckInPalace(int x, int y, BaseObject* obj) const;
    int GetPalaceGuildCount() const;

private:
    // 内部辅助函数
    void InitializeDefenseUnits();
    void CreateDefenseUnit(DefenseUnit& unit, const std::string& mapName);
    bool LoadDefenseUnitConfig(const std::string& section, DefenseUnit& unit);
    void SaveDefenseUnitConfig(const std::string& section, const DefenseUnit& unit);
    void UpdateDefenseUnitStatus();
    void ProcessWarLogic();
    void ProcessIncomeLogic();
    bool InAttackerList(Guild* guild) const;
    bool CreateDefaultConfig();
};

// 城堡管理器类（对应原项目的TCastleManager）
class CastleManager {
private:
    std::vector<std::unique_ptr<Castle>> m_castles;     // 城堡列表
    std::unordered_map<std::string, Castle*> m_castleMap; // 城堡名称映射
    mutable std::mutex m_mutex;                         // 线程安全锁
    bool m_initialized;                                 // 初始化状态
    std::string m_configPath;                          // 配置路径

public:
    // 单例模式
    static CastleManager& GetInstance();

    // 初始化和清理
    bool Initialize(const std::string& configPath = "Castle");
    void Finalize();

    // 城堡管理
    bool LoadCastleList();
    bool SaveCastleList();
    Castle* FindCastle(const std::string& castleName);
    Castle* GetCastle(int index);
    int GetCastleCount() const;

    // 战争区域检查
    Castle* InCastleWarArea(BaseObject* obj);
    Castle* InCastleWarArea(Environment* env, int x, int y);

    // 城堡成员检查
    Castle* IsCastleMember(BaseObject* obj);
    Castle* IsCastlePalaceEnvironment(Environment* env);
    Castle* IsCastleEnvironment(Environment* env);

    // 经济系统
    void GetCastleGoldInfo(std::vector<std::string>& info);
    void GetCastleNameList(std::vector<std::string>& names);
    void IncomeGold(int gold);

    // 运行时处理
    void Run();
    void Save();

    // 线程安全
    void Lock() { m_mutex.lock(); }
    void Unlock() { m_mutex.unlock(); }

public:
    CastleManager();
    ~CastleManager();

private:
    CastleManager(const CastleManager&) = delete;
    CastleManager& operator=(const CastleManager&) = delete;

    // 内部辅助函数
    bool CreateDefaultCastle();
    void ClearCastles();
};

// 全局城堡管理器实例
extern std::unique_ptr<CastleManager> g_CastleManager;

} // namespace MirServer
